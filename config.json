{"host": "localhost", "username": "root", "password": "", "database": "telegram_scraper", "llm_timeout": 240, "server": {"host": "127.0.0.1", "port": 9096}, "openai": "https://api.openai.com/v1/chat/completions", "anthropic": "https://api.anthropic.com/v1/messages", "gemini": "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions", "xai": "https://api.x.ai/v1/chat/completions", "deepseek": "https://api.deepseek.com/chat/completions", "security": {"console_bearer_token": "abcd1234"}, "application": {"log_level": "INFO", "batch_size": 100, "historical_scraping_days": 90, "min_content_length": 2, "max_content_length": 10000, "debug_mode": false, "bypass_content_validation": false, "validation_thresholds": {"fake_content_confidence": 0.7, "trading_signal_confidence": 0.7, "roleplay_confidence": 0.7, "min_word_count": 2, "min_response_length": 10, "suspicious_pattern_confidence": 0.8, "generated_content_quality_threshold": 0.8}, "content_filtering": {"enable_langgraph_validation": true, "strict_trading_signal_validation": true, "block_fake_precision_pricing": true, "block_unrealistic_guarantees": true, "block_roleplay_content": true, "log_filtered_content": true}}, "telegram": {"validate_config_on_startup": true, "test_api_connection": false, "retry_failed_posts": true}, "crypto": {"default_assets": ["BTC", "ETH", "SOL", "ADA", "DOT"], "analysis_depth": "detailed", "signal_confidence_threshold": 0.7, "sentiment_analysis": true, "trading_signal_generation": true, "system_prompt": "You are an expert content analyst. Analyze the input with strict accuracy. Prioritize facts, extract actionable insights, and avoid speculation. Never invent details. Respond only in the exact format specified.", "user_prompt": "Analyze this message and provide a concise summary using this exact structure:\n\nSummary: [1-2 sentences capturing core meaning and context]\nKey Points:\n- [Bullet 1: Most important fact]\n- [Bullet 2: Supporting detail or implication]\n- [Bullet 3: Hidden nuance or risk, if any]\nSentiment: [positive / negative / neutral] ([Score: -5 to +5])\nActionable Insights:\n- [Specific next step or decision required]\n- [Stakeholder or owner, if implied]\n(or \"N/A\" if none)\n\nInput:\n{message_content}", "crypto_system_prompt": "You are a professional cryptocurrency market analyst with expertise in on-chain data, technical indicators, and social sentiment analysis. Your role is to extract the SINGLE most actionable trading signal from any input. Ignore noise, rumors, and speculative hype. Only respond if a clear, evidence-based signal can be identified. Never guess. If uncertain, output 'Signal: N/A | Confidence: 0/10'. Prioritize verifiable triggers: volume spikes, key support/resistance breaks, whale activity, or major protocol updates.", "crypto_user_prompt": "Analyze this crypto message and return ONE consolidated trading signal ONLY — no explanations, no lists, no multiple signals. Focus exclusively on the strongest, most actionable signal based on price action, on-chain data, or verified catalysts:\n\n{message_content}\n\nFormat strictly:\nToken: [symbol only, e.g., BTC, ETH, SOL] | Sentiment: [bullish/bearish/neutral] | Insight: [1 sentence max — cite concrete trigger: e.g., 'Broke 200-day MA with 3x volume surge'] | Signal: [buy/sell/hold] | Confidence: [1-10]/10\n\nRules:\n- Token must be a valid ticker (no full names like 'Ethereum' — use 'ETH')\n- Insight must reference observable data (not opinion)\n- Confidence = 1–10 based on signal strength + data reliability (e.g., whale transfer + DEX volume spike = 8–9)\n- If signal is vague, emotional, or unverifiable → Confidence: 0/10 & Signal: N/A", "content_generation": {"default_type": "article", "max_length": 2000, "style_guides": {"article": {"tone": "informative", "structure": "introduction, body, conclusion"}, "social_post": {"tone": "engaging", "max_length": 280}, "report": {"tone": "professional", "structure": "executive_summary, findings, recommendations"}}}}}